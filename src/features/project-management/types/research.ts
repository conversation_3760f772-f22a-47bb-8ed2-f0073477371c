import type { ItemFrameworkResponse } from '@/features/frameworks-templates/types';
import type { IFileResponse } from '@/shared/types/global';

export type CreateProjectResearch = {
  name: string;
  stepId: string;
  order: number;
  files?: ResearchTemplateCustomFile[];
  infos: {
    framework?: ItemFrameworkResponse | null;
    researchType?: ResearchFramework;
    template: IFileResponse[];
    files: IFileResponse[];
    otherTemplate?: string;
    frameWorkId: string;
    input?: string[];
    type?: ReportType;
    templateId?: string;
  }[];
};

export type ResearchInfo = {
  template: IFileResponse[];
  researchType?: ResearchFramework;
  framework?: ItemFrameworkResponse | null;
  files?: IFileResponse[];
  otherTemplate?: string;
  frameWorkId: string;
  input?: string[];
  type?: ReportType;
  templateId?: string;
};

export type ResearchItem = {
  id: string;
  name: string;
  order: number;
  infos: ResearchInfo[];
  stepId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
};

export type ResearchQueryParams = {
  itemsPerPage?: number;
  page?: number;
  searchValue?: string;
  stepId?: string;
};

export type GetResearchResponse = {
  items: ResearchItem[];
  total: number;
  page: number;
  itemsPerPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type UpdateProjectResearch = {
  name: string;
  files?: ResearchTemplateCustomFile[];
  infos: {
    template: IFileResponse[];
    framework?: ItemFrameworkResponse | null;
    files?: IFileResponse[];
    otherTemplate?: string;
    frameWorkId: string;
    input?: string[];
    type?: ReportType;
    templateId?: string;
    researchType?: ResearchFramework;
  }[];
};

export enum ResearchFramework {
  DeskResearch,
  QuantitativeResearch,
  QualitativeResearch,
  Other,
}

export const frameworkOptions = [
  { value: ResearchFramework.DeskResearch, label: 'Desk Research' },
  { value: ResearchFramework.QuantitativeResearch, label: 'Quantitative Research' },
  { value: ResearchFramework.QualitativeResearch, label: 'Qualitative Research' },
  { value: ResearchFramework.Other, label: 'Other' },
];

export const CONVERT_RESEARCH_FRAMEWORK_TO_LABEL: { [key: string]: string } = {
  [ResearchFramework.DeskResearch]: 'Desk Research',
  [ResearchFramework.QualitativeResearch]: 'Qualitative Research',
  [ResearchFramework.QuantitativeResearch]: 'Quantitative Research',
  [ResearchFramework.Other]: 'Other',
};

// Report Type enum for document research
export enum ReportType {
  Report,
  PresentationDeck,
}

export const CONVERT_RESPORT_TYPE_TO_LABEL: { [key: string]: string } = {
  [ReportType.Report]: 'Report',
  [ReportType.PresentationDeck]: 'Presentation Deck',
};

export const reportTypeOptions = [
  { value: ReportType.Report, label: 'Report' },
  { value: ReportType.PresentationDeck, label: 'Presentation Deck' },
];

export type ResearchTemplateCustomFile = {
  originalname: string;
  mimetype: string;
  size: string;
  url: string;
  key: string;
  bucket: string;
  provider: string;
  category: string;
};
