'use client';

import { useEffect, useRef, useState } from 'react';
import { useCoAgent } from '@copilotkit/react-core';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useCurrentStep, useCurrentTask, useWorkflowActions, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { EEndpointApiCopilotkit, ENameStateAgentCopilotkit } from '@/shared/enums/global';
import BaseBriefAnalysis from './BaseBriefAnalysis';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { useParams } from 'next/navigation';
import type { ProjectCampaignEnum, TemplateFiles } from '@/features/project-management/types/project';
import { ETypeFile } from '@/features/project-management/types/project';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { htmlToMarkdownVer2, markdownToHtmlVer2 } from '@/shared/components/ui/editor/parser';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';

type BriefAnalysisResponse = {
  infos: { value: string }[];
  isGenerate: boolean;
};

const BriefStandardizedWrapper: React.FC = () => {
  const [isShowEditButton, _setIsShowEditButton] = useState(false);

  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);

  const [campaignSelected, setCampaignSelected] = useState<ProjectCampaignEnum | null>(null);

  const [isSaved, _setIsSaved] = useState(true);

  const [isClickUnSaved, setIsClickUnSaved] = useState(false);

  const [isShowModal, setIsShowModal] = useState(false);

  const [isShowReGen, setIsShowReGen] = useState(false);

  const titleConfirm = 'Confirm Changes';

  const titleUnSave = 'UnSave Changes';

  const descriptionUnSave = 'The changes made will be lost. Do you want to proceed?';

  const descriptionConfirm = 'Are you sure you want to make this change? Changing the status will result in the deletion of all related information for this step';

  const [titlePopup, setTitlePopUp] = useState<string>(titleConfirm);

  const [descriptionPopUp, setDescriptionPopUp] = useState<string>(descriptionConfirm);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync } = useUpdateStatusStep();

  const { registerStep, clearStep } = useDirty();

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const currentStepId = currentStep?.id;

  const workflow = useWorkflowTasks();

  const idSecondStep = workflow[1]?.steps[0]?.id;

  const { data: clientUploadData } = useGetInfoDetail<any, any>(idSecondStep ?? '');

  const { data: briefStandardized } = useGetInfoDetail<BriefAnalysisResponse, any>(currentStep?.id ?? '');

  const { data: templates } = useGetListTemplates();

  const params = useParams<{ id: string }>();

  const abortControllerRef = useRef<AbortController | null>(null);

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: 'Unsaved Changes',
    message: 'You have unsaved changes in the form. Are you sure you want to leave?',
  });

  useEffect(() => {
    if (clientUploadData?.stepInfo.length && clientUploadData?.stepInfo[0]?.infos?.length) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setCampaignSelected(clientUploadData?.stepInfo[0]?.infos[0]?.serviceOption);
    }
  }, [clientUploadData]);

  useEffect(() => {
    if (templates) {
      const templateSelect = templates.filter(template => template.campaign === campaignSelected);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);

      setTemplateFile(urlOptions);
    }
  }, [templates, campaignSelected]);

  const {
    completeStep,
    updateStatus,
  } = useWorkflowActions();

  const { state } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
      agent_name: AGENT_NAME_COPILOTKIT.ANALYSIS,
    },
  });

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };

  const saveDataFromAI = async (markdown: string) => {
    // convert same format data
    const html = await markdownToHtmlVer2(markdown);
    const markDownConvert = htmlToMarkdownVer2(html);

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markDownConvert }],
        },
      ],
    };

    if (currentStepId) {
      await updateQuestionAnswer(payload, currentStepId);
      mutateAsync({ id: currentStepId, status: EStatusTask.IN_PROGRESS });
    }
  };

  const getAnalysisBriefData = async (data: any) => {
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.ANALYSIS }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const brief = res.data.result;

      updateMarkdownToState(brief);
      saveDataFromAI(brief);
    } catch (error: any) {
      console.log(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (briefStandardized && briefStandardized?.stepInfo.length) {
      const markdown = briefStandardized.stepInfo[0]?.infos[0]?.value;
      const isReGen = briefStandardized.stepInfo[0]?.isGenerate;
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsShowReGen(isReGen ?? false);

      updateMarkdownToState(markdown ?? '');
      // if (currentStep?.status !== EStatusTask.COMPLETED) {
      //   // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      //   setIsShowEditButton(false);
      // } else {
      //   // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      //   setIsShowEditButton(true);
      // }
    } else {
      if (briefStandardized?.stepInfoPrevious.length) {
        const briefFile = briefStandardized?.stepInfoPrevious[0].infos[0];
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsShowReGen(false);
        if (!templateFile.length) {
          return;
        }
        const data = {
          project_id: params.id,
          answer_brief_output: briefFile.value,
          ...templateFile.reduce((result, template) => {
            if (template.type === ETypeFile.BRIEF_TEMPLATE) {
              result.template_brief_url = [
                ...(result.template_brief_url || []),
                ...getFile([template.file]),
              ];
            }
            if (template.type === ETypeFile.BRIEF_QUESTION) {
              result.question_brief_url = [
                ...(result.question_brief_url || []),
                ...getFile([template.file]),
              ];
            }
            return result;
          }, {} as any),
        };
        getAnalysisBriefData(data);
      }
    }
  }, [briefStandardized, templateFile]);

  const handleReGenBrief = () => {
    if (!currentStepId) {
      return;
    }
    setIsLoading(true);
    updateStatus(currentStepId, EStatusTask.IN_PROGRESS);
    updateStatus(currentTask?.id ?? '', EStatusTask.IN_PROGRESS, true);
    updateQuestionAnswer({ stepInfos: [] }, currentStepId);
  };

  useEffect(() => {
    const briefAnalysisState = state[ENameStateAgentCopilotkit.ANALYSIS];
    if (briefAnalysisState && briefAnalysisState.brief_analysis_output && briefAnalysisState.brief_analysis_process && briefAnalysisState.brief_analysis_process === 'done') {
      updateMarkdownToState(briefAnalysisState.brief_analysis_output);
    }
  }, [state]);

  const compareMarkdown = (form?: string) => {
    const markdownInitial = briefStandardized?.stepInfo[0]?.infos[0]?.value ?? '';
    const markdownCurrent = markdown;

    return markdownInitial === (form || markdownCurrent);
  };

  const toggleEditMode = () => {
    setIsEditMode(prev => !prev);
  };

  const handleFinishStep = async (form?: string) => {
    if (!currentStepId) {
      return;
    }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: form ?? markdown }],
        },
      ],
    };

    await mutateAsync({ id: currentStepId, status: EStatusTask.COMPLETED, select: 'all', isGenerate: true, stepIds: [], stepInfoIds: [] });

    await updateQuestionAnswer(payload, currentStepId);

    _setIsSaved(true);

    clearStep(currentStepId);
  };

  const handleApprove = async () => {
    if (!currentStepId) {
      return;
    }

    setIsEditMode(false);

    if (currentStep.status !== EStatusTask.COMPLETED) {
      handleFinishStep();
      await mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }
    if (currentTask && currentTask.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
    }

    completeStep(currentStepId);
  };

  const handleEditorChange = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);

    if (!currentStepId) {
      return;
    }
    const isChanged = compareMarkdown(markdown);

    _setIsSaved(isChanged);
    registerStep(currentStepId, () => !isChanged);
  };

  const discardChange = () => {
    if (!currentStep) {
      return;
    }
    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setTitlePopUp(titleUnSave);
      setDescriptionPopUp(descriptionUnSave);
      setIsClickUnSaved(true);
      setIsShowModal(true);
      return;
    }

    clearStep(currentStepId ?? '');
    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    if (!currentStep) {
      return;
    }
    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setIsClickUnSaved(false);
      setTitlePopUp(titleConfirm);
      setDescriptionPopUp(descriptionConfirm);
      setIsShowModal(true);
      return;
    }

    clearStep(currentStepId ?? '');
    setMarkdown(form);
    setIsEditMode(false);
  };

  const handleConfirmPopUp = () => {
    if (isClickUnSaved) {
      setForm(markdown);
      setIsEditMode(false);
      setIsShowModal(false);
      return;
    }
    clearStep(currentStepId ?? '');
    handleFinishStep(form);
    setMarkdown(form);
    setIsEditMode(false);
    setIsShowModal(false);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  return (
    <>
      <BaseBriefAnalysis
        isLoading={isLoading}
        markdown={markdown}
        form={form}
        isEditMode={isEditMode}
        isShowEditButton={isShowEditButton}
        isShowButtonReGen={isShowReGen}
        onEditToggle={toggleEditMode}
        onConfirmChange={confirmChange}
        onDiscardChange={discardChange}
        onEditorChange={handleEditorChange}
        onApprove={handleApprove}
        onReGen={handleReGenBrief}
      />

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titlePopup}
        description={descriptionPopUp}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText="Continue"
        cancelText="Cancel"
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </>
  );
};

export default BriefStandardizedWrapper;
