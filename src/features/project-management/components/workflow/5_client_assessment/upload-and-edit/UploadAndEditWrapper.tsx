import { useMemo, useState } from 'react';
import UploadFileResearch from './UploadFileResearch';
import TextUploadWrapper from './TextUploadWrapper';
import { EDeskResearch } from '@/features/project-management/types/questionnaire';
import type { IFileResponse } from '@/shared/types/global';

type UploadAndEditType = {
  status: 'upload' | 'editor';
  data: any[];
  id: string;
  stepId: string;
  templates: IFileResponse[];
  evaluationFramework: string;
  onBackDashboard: () => void;
  onOpenDetailScore: (data: string) => void;
};

const UploadAndEditWrapper: React.FC<UploadAndEditType> = ({
  status,
  data,
  id,
  stepId,
  templates,
  evaluationFramework,
  onBackDashboard,
  onOpenDetailScore,
}) => {
  const [view, setView] = useState<'upload' | 'editor'>(() => status);

  const [markdown, setMarkdown] = useState<string>('');

  const dataFormFileSearch = useMemo(() => {
    const item = data.find(d => d.type === EDeskResearch.UPLOAD_FILE);
    return item ? item.infos : [];
  }, [data]);

  return (
    view === 'upload'
      ? (
          <UploadFileResearch
            type={EDeskResearch.UPLOAD_FILE}
            data={dataFormFileSearch}
            id={id}
            stepId={stepId}
            initialData={data}
            changeNextView={setView}
            onBackDashboard={onBackDashboard}
            showViewButton={!!markdown}
            onViewData={() => setView('editor')}
          />
        )
      : (
          <TextUploadWrapper
            id={id}
            stepId={stepId}
            data={data}
            templates={templates}
            evaluationFramework={evaluationFramework}
            onBackDashboard={onBackDashboard}
            onBackUploadFile={() => setView('upload')}
            getMarkdown={setMarkdown}
            onOpenDetailScore={onOpenDetailScore}
          />
        )

  );
};

export default UploadAndEditWrapper;
