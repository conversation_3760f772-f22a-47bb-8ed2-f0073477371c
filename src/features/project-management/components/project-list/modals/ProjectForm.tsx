'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useCallback, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import Input from '@/shared/components/form/input/InputField';
import TextArea from '@/shared/components/form/input/TextArea';
import Label from '@/shared/components/form/Label';
import Select from '@/shared/components/form/Select';
import SearchableMultiSelect from '@/shared/components/form/SearchableMultiSelect';
import { Button } from '@/shared/components/ui/button';
import { Calendar } from '@/shared/components/ui/calendar';
import { Modal } from '@/shared/components/ui/modal';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import { ProjectCampaignEnum, ProjectStatusEnum, ProjectTypeEnum } from '@/features/project-management/types/project';
import type { Project } from '@/features/project-management/types/project';
import { useTeamMembersForProject } from '@/features/project-management/hooks';
import { createProjectSchema } from '@/features/project-management/validation/project.validation';
import type { CreateProjectPayload } from '@/features/project-management/validation/project.validation';

type ProjectFormModalType = {
  isOpen: boolean;
  project: Project;
  closeModal: () => void;
};

const ProjectFromModal: React.FC<ProjectFormModalType> = (
  {
    isOpen,
    project,
    closeModal,
  },
) => {
  const t = useTranslations('Project');
  // const { isOpen, openModal, closeModal } = useModal();
  const { teamMemberOptions, isLoading: isLoadingTeamMembers, handleSearch } = useTeamMembersForProject();
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  // Default form values
  const defaultValues = useMemo(() => {
    console.log('>>>>>', project);
    return (
      {
        // Project information
        name: project?.name ?? '',
        type: project?.type ?? ProjectTypeEnum.BRANDING,
        campaign: project?.campaign ?? ProjectCampaignEnum.CORPORATE,
        description: project?.description ?? '',
        status: project?.status ?? ProjectStatusEnum.PLANNED,
        startDate: project?.startDate ?? new Date().toISOString(),
        endDate: project?.endDate ?? '',
        memberIds: project?.memberIds ?? [],
        // Client information
        clientName: project?.clientName ?? '',
        address: project?.address ?? '',
        taxCode: project?.taxCode ?? '',
        contactPerson: project?.contactPerson ?? '',
        tel: project?.tel ?? '',
        email: project?.email ?? '',
        industry: project?.industry ?? '',
      }
    );
  }, [project]);

  console.log(project);
  console.log(defaultValues);

  // Initialize React Hook Form with Zod validation
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CreateProjectPayload>({
    resolver: zodResolver(createProjectSchema),
    defaultValues,
  });

  const resetForm = useCallback(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const closeModalForm = () => {
    resetForm();
    closeModal();
  };

  const onSubmit = async (data: CreateProjectPayload) => {
    // Prepare the payload
    const projectPayload: CreateProjectPayload = {
      // Project information
      name: data.name,
      type: data.type,
      campaign: data.campaign,
      description: data.description || undefined,
      status: ProjectStatusEnum.PLANNED, // Default status
      startDate: data.startDate,
      endDate: data.endDate || undefined,
      memberIds: data.memberIds || [],

      // Client information
      clientName: data.clientName,
      address: data.address,
      taxCode: data.taxCode,
      contactPerson: data.contactPerson,
      tel: data.tel,
      email: data.email,
      industry: data.industry || '',
    };

    try {
      // await createProject(projectPayload);
      resetForm();
      closeModalForm();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Failed to create project:', error);
    }
  };

  const handleBackView = () => {
    reset();
    setIsEditMode(false);
  };

  const projectTypeOptions = [
    { value: ProjectTypeEnum.BRANDING.toString(), label: t('project_type_options.branding') },
    { value: ProjectTypeEnum.GENERAL_CONSULTING.toString(), label: t('project_type_options.general_consulting') },
    { value: ProjectTypeEnum.DIAGNOSTICS.toString(), label: t('project_type_options.diagnostics') },
  ];

  const campaignOptions = [
    { value: ProjectCampaignEnum.CORPORATE.toString(), label: t('campaign_options.corporate') },
    { value: ProjectCampaignEnum.CRISIS_MANAGEMENT.toString(), label: t('campaign_options.crisis_management') },
    { value: ProjectCampaignEnum.EVENT.toString(), label: t('campaign_options.event') },
    { value: ProjectCampaignEnum.GR_ADVOCACY.toString(), label: t('campaign_options.gr_advocacy') },
    { value: ProjectCampaignEnum.IMC.toString(), label: t('campaign_options.imc') },
    { value: ProjectCampaignEnum.MARKET_RESEARCH.toString(), label: t('campaign_options.market_research') },
    { value: ProjectCampaignEnum.MEDIA_RELATION_PR.toString(), label: t('campaign_options.media_relation_pr') },
    { value: ProjectCampaignEnum.MI_BRAND_BRANDING.toString(), label: t('campaign_options.mi_brand_branding') },
    { value: ProjectCampaignEnum.PRODUCT_LAUNCH.toString(), label: t('campaign_options.product_launch') },
    { value: ProjectCampaignEnum.SOCIAL_DIGITAL_CORPORATE.toString(), label: t('campaign_options.social_digital_corporate') },
    { value: ProjectCampaignEnum.SOCIAL_DIGITAL_PRODUCT.toString(), label: t('campaign_options.social_digital_product') },
    { value: ProjectCampaignEnum.TVC_VIDEO_PRODUCTION.toString(), label: t('campaign_options.tvc_video_production') },
  ];

  return (
    <>
      <Modal isOpen={isOpen} onClose={closeModalForm} className="max-w-[700px] m-4">
        <div className="no-scrollbar relative w-full max-w-[700px] rounded-3xl bg-white p-6 dark:bg-gray-900 overflow-hidden">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
              {t('form_title_detail')}
            </h4>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
            <div className="custom-scrollbar h-[450px] overflow-y-auto px-2 pb-3">
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                {/* Project Information Section */}
                <div className="col-span-2 mb-2">
                  <h5 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                    {t('project_section')}
                  </h5>
                </div>

                {/* Project Name */}
                <div className="col-span-2">
                  <Label htmlFor="name">
                    {t('project_name')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="name"
                    render={({ field }) => (
                      <Input
                        id="name"
                        {...field}
                        placeholder={t('placeholder.project_name')}
                        type="text"
                        error={!!errors.name}
                        hint={errors.name?.message}
                      />
                    )}
                  />
                </div>

                {/* Project Type */}
                <div className="col-span-1">
                  <Label htmlFor="type">
                    {t('project_type')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="type"
                    render={({ field }) => (
                      <Select
                        options={projectTypeOptions}
                        defaultValue={field.value.toString()}
                        onChange={value => field.onChange(Number(value))}
                        placeholder={t('placeholder.project_type')}
                        className="w-full"
                      />
                    )}
                  />
                </div>

                {/* Campaign */}
                <div className="col-span-1">
                  <Label htmlFor="campaign">
                    {t('campaign')}
                  </Label>
                  <Controller
                    control={control}
                    name="campaign"
                    render={({ field }) => (
                      <Select
                        options={campaignOptions}
                        defaultValue={field.value.toString()}
                        onChange={value => field.onChange(Number(value))}
                        placeholder={t('placeholder.campaign')}
                        className="w-full"
                      />
                    )}
                  />
                </div>

                {/* Team Members */}
                <div className="col-span-2">
                  <Controller
                    control={control}
                    name="memberIds"
                    render={({ field }) => (
                      <SearchableMultiSelect
                        label={t('team_members')}
                        options={teamMemberOptions}
                        onChange={field.onChange}
                        onSearch={handleSearch}
                        disabled={isLoadingTeamMembers}
                        placeholder={t('placeholder.select_team_members')}
                        searchPlaceholder={t('placeholder.search_team_members')}
                        isLoading={isLoadingTeamMembers}
                      />
                    )}
                  />
                </div>

                {/* Description */}
                <div className="col-span-2">
                  <Label htmlFor="description">
                    {t('description')}
                  </Label>
                  <Controller
                    control={control}
                    name="description"
                    render={({ field }) => (
                      <TextArea
                        {...field}
                        placeholder={t('placeholder.description')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                        rows={3}
                      />
                    )}
                  />
                </div>

                {/* Start Date */}
                <div className="col-span-1">
                  <Label htmlFor="startDate">
                    {t('start_date')}
                  </Label>
                  <Controller
                    control={control}
                    name="startDate"
                    render={({ field }) => (
                      <Popover>
                        <PopoverTrigger className="w-full flex items-center justify-between px-3 py-2 text-sm border border-gray-200 rounded-md dark:border-gray-700" id="start-date">
                          {field.value ? format(new Date(field.value), 'PPP') : <span className="text-gray-500">{t('placeholder.start_date')}</span>}
                          <CalendarIcon className="h-4 w-4 opacity-50" />
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 z-100000" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={(date: Date | undefined) => field.onChange(date ? date.toISOString() : '')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    )}
                  />
                </div>

                {/* End Date */}
                <div className="col-span-1">
                  <Label htmlFor="endDate">
                    {t('end_date')}
                  </Label>
                  <Controller
                    control={control}
                    name="endDate"
                    render={({ field, formState }) => {
                      const startDate = formState.defaultValues?.startDate;
                      return (
                        <Popover>
                          <PopoverTrigger className="w-full flex items-center justify-between px-3 py-2 text-sm border border-gray-200 rounded-md dark:border-gray-700" id="end-date">
                            {field.value ? format(new Date(field.value), 'PPP') : <span className="text-gray-500">{t('placeholder.end_date')}</span>}
                            <CalendarIcon className="h-4 w-4 opacity-50" />
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0 z-100000" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value ? new Date(field.value) : undefined}
                              onSelect={(date: Date | undefined) => field.onChange(date ? date.toISOString() : '')}
                              initialFocus
                              disabled={date => startDate ? date < new Date(startDate) : false}
                            />
                          </PopoverContent>
                        </Popover>
                      );
                    }}
                  />
                </div>

                {/* Client Information Section */}
                <div className="col-span-2 mt-4 mb-2">
                  <h5 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                    {t('client_section')}
                  </h5>
                </div>

                {/* Client's Name */}
                <div className="col-span-2">
                  <Label htmlFor="clientName">
                    {t('client_name')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="clientName"
                    render={({ field }) => (
                      <Input
                        id="clientName"
                        {...field}
                        placeholder={t('placeholder.client_name')}
                        type="text"
                        error={!!errors.clientName}
                        hint={errors.clientName?.message}
                      />
                    )}
                  />
                </div>

                {/* Address */}
                <div className="col-span-1">
                  <Label htmlFor="address">
                    {t('address')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="address"
                    render={({ field }) => (
                      <Input
                        id="address"
                        {...field}
                        placeholder={t('placeholder.address')}
                        type="text"
                        error={!!errors.address}
                        hint={errors.address?.message}
                      />
                    )}
                  />
                </div>

                {/* Tax Code */}
                <div className="col-span-1">
                  <Label htmlFor="taxCode">
                    {t('tax_code')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="taxCode"
                    render={({ field }) => (
                      <Input
                        id="taxCode"
                        {...field}
                        placeholder={t('placeholder.tax_code')}
                        type="text"
                        error={!!errors.taxCode}
                        hint={errors.taxCode?.message}
                      />
                    )}
                  />
                </div>

                {/* Contact Person */}
                <div className="col-span-1">
                  <Label htmlFor="contactPerson">
                    {t('contact_person')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="contactPerson"
                    render={({ field }) => (
                      <Input
                        id="contactPerson"
                        {...field}
                        placeholder={t('placeholder.contact_person')}
                        type="text"
                        error={!!errors.contactPerson}
                        hint={errors.contactPerson?.message}
                      />
                    )}
                  />
                </div>

                {/* Tel */}
                <div className="col-span-1">
                  <Label htmlFor="tel">
                    {t('tel')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="tel"
                    render={({ field }) => (
                      <Input
                        id="tel"
                        {...field}
                        placeholder={t('placeholder.tel')}
                        type="tel"
                        error={!!errors.tel}
                        hint={errors.tel?.message}
                      />
                    )}
                  />
                </div>

                {/* Email */}
                <div className="col-span-1">
                  <Label htmlFor="email">
                    {t('email')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="email"
                    render={({ field }) => (
                      <Input
                        id="email"
                        {...field}
                        placeholder={t('placeholder.email')}
                        type="email"
                        error={!!errors.email}
                        hint={errors.email?.message}
                      />
                    )}
                  />
                </div>

                {/* Industry */}
                <div className="col-span-1">
                  <Label htmlFor="industry">
                    {t('industry')}
                  </Label>
                  <Controller
                    control={control}
                    name="industry"
                    render={({ field }) => (
                      <Input
                        id="industry"
                        {...field}
                        placeholder={t('placeholder.industry')}
                        type="text"
                      />
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 px-2 mt-6 lg:justify-end">
              {!isEditMode
                ? (
                    <Button onClick={() => setIsEditMode(true)}>
                      { t('edit_button') }
                    </Button>
                  )
                : (
                    <React.Fragment>
                      <Button variant="outline" onClick={handleBackView}>
                        { t('back_button') }
                      </Button>
                      <Button variant="outline" onClick={closeModalForm} disabled={isSubmitting}>
                        {t('cancel_button')}
                      </Button>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? t('update_button') : t('updating_button')}
                      </Button>
                    </React.Fragment>
                  )}
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
};

export default ProjectFromModal;
