{"name": "next-js-boilerplate", "version": "1.0.0", "author": "Ixartz (https://github.com/ixartz)", "engines": {"node": ">=20"}, "scripts": {"dev:spotlight": "spotlight-sidecar", "dev:next": "next dev --turbo --port 3001", "dev": "run-p dev:*", "build": "next build", "start": "next start --port 3001", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next out coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "check-types": "tsc --noEmit --pretty", "test": "vitest run", "test:e2e": "playwright test", "commit": "cz", "db:generate": "drizzle-kit generate", "db:migrate": "dotenv -c production -- drizzle-kit migrate", "db:studio": "dotenv -c production -- drizzle-kit studio", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://127.0.0.1:6006 test-storybook", "prepare": "husky"}, "dependencies": {"@arcjet/next": "1.0.0-beta.4", "@commitlint/types": "^19.8.0", "@copilotkit/react-core": "^1.8.11", "@copilotkit/react-ui": "^1.8.11", "@copilotkit/runtime": "^1.8.11", "@electric-sql/pglite": "^0.2.17", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.11", "@sentry/nextjs": "^8.55.0", "@spotlightjs/spotlight": "^2.12.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.76.1", "apexcharts": "^4.5.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "flatpickr": "^4.6.13", "framer-motion": "^12.9.2", "jodit-react": "^5.2.19", "lucide-react": "^0.487.0", "marked": "^15.0.12", "motion": "^12.6.3", "next": "^15.2.4", "next-intl": "^3.26.5", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "pg": "^8.14.1", "react": "19.0.0", "react-apexcharts": "^1.7.0", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-quill-new": "^3.4.6", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "turndown": "^7.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@commitlint/cz-commitlint": "^19.8.0", "@eslint-react/eslint-plugin": "^1.38.0", "@faker-js/faker": "^9.6.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@next/bundle-analyzer": "^15.2.4", "@next/eslint-plugin-next": "^15.2.4", "@percy/cli": "1.30.7", "@percy/playwright": "^1.0.7", "@playwright/test": "^1.51.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@storybook/addon-essentials": "^8.6.10", "@storybook/addon-interactions": "^8.6.10", "@storybook/addon-links": "^8.6.10", "@storybook/addon-onboarding": "^8.6.10", "@storybook/blocks": "^8.6.10", "@storybook/nextjs": "^8.6.10", "@storybook/react": "^8.6.10", "@storybook/test": "^8.6.10", "@storybook/test-runner": "^0.22.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.0.17", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/js-cookie": "^3.0.6", "@types/node": "^22.13.13", "@types/pg": "^8.11.11", "@types/react": "^19.0.12", "@types/turndown": "^5.0.5", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.9", "@vitest/expect": "^3.0.9", "checkly": "^5.1.0", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9.23.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-testing-library": "^7.1.1", "http-server": "^14.1.1", "husky": "^9.1.7", "js-cookie": "^3.0.5", "jsdom": "^26.0.0", "lint-staged": "^15.5.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.3", "postcss-load-config": "^6.0.1", "rimraf": "^6.0.1", "semantic-release": "^24.2.3", "start-server-and-test": "^2.0.11", "storybook": "^8.6.10", "tailwindcss": "^4.0.17", "ts-node": "^10.9.2", "typescript": "^5.8.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.9", "vitest-fail-on-console": "^0.7.1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "release": {"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits"}], "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", "@semantic-release/github"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}}